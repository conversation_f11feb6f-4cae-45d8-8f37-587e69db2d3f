#!/bin/bash

# =============================================================================
# Docker in Docker 构建示例脚本
# 此脚本展示了如何在 LiteOps CI/CD 任务中使用 Docker 命令
# =============================================================================

set -e

echo "🐳 开始 Docker 构建示例..."

# 检查 Docker 环境
echo "📋 检查 Docker 环境..."
docker version
docker info

# 示例1: 构建简单的 Web 应用
echo "🏗️  示例1: 构建 Node.js Web 应用"

# 创建示例应用
mkdir -p /tmp/webapp
cd /tmp/webapp

# 创建 package.json
cat > package.json << 'EOF'
{
  "name": "webapp-example",
  "version": "1.0.0",
  "description": "Docker in Docker example",
  "main": "app.js",
  "scripts": {
    "start": "node app.js"
  },
  "dependencies": {
    "express": "^4.18.0"
  }
}
EOF

# 创建应用文件
cat > app.js << 'EOF'
const express = require('express');
const app = express();
const port = 3000;

app.get('/', (req, res) => {
  res.send(`
    <h1>Hello from Docker in Docker!</h1>
    <p>Build Number: ${process.env.BUILD_NUMBER || 'unknown'}</p>
    <p>Version: ${process.env.VERSION || 'unknown'}</p>
    <p>Environment: ${process.env.ENVIRONMENT || 'unknown'}</p>
  `);
});

app.listen(port, () => {
  console.log(`App listening at http://localhost:${port}`);
});
EOF

# 创建 Dockerfile
cat > Dockerfile << 'EOF'
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .

EXPOSE 3000

CMD ["npm", "start"]
EOF

# 构建镜像
echo "🔨 构建 Docker 镜像..."
docker build -t webapp-example:${VERSION:-latest} .

# 示例2: 多阶段构建
echo "🏗️  示例2: 多阶段构建示例"

mkdir -p /tmp/multistage
cd /tmp/multistage

# 创建多阶段 Dockerfile
cat > Dockerfile << 'EOF'
# 构建阶段
FROM node:16-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# 运行阶段
FROM node:16-alpine AS runtime

WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .

EXPOSE 3000
CMD ["npm", "start"]
EOF

# 复制应用文件
cp /tmp/webapp/package.json .
cp /tmp/webapp/app.js .

# 构建多阶段镜像
echo "🔨 构建多阶段 Docker 镜像..."
docker build -t webapp-multistage:${VERSION:-latest} .

# 示例3: 使用 Docker Compose
echo "🐙 示例3: Docker Compose 示例"

mkdir -p /tmp/compose-example
cd /tmp/compose-example

# 创建 docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  web:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - BUILD_NUMBER=${BUILD_NUMBER}
      - VERSION=${VERSION}
    depends_on:
      - redis

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"

volumes:
  redis_data:
EOF

# 复制应用文件
cp /tmp/webapp/* .

# 使用 Docker Compose 构建
echo "🔨 使用 Docker Compose 构建..."
docker-compose build

# 示例4: 推送到镜像仓库（示例，需要配置认证）
echo "📤 示例4: 推送镜像到仓库"

# 标记镜像
docker tag webapp-example:${VERSION:-latest} registry.example.com/webapp:${VERSION:-latest}

# 推送镜像（需要先登录）
# docker push registry.example.com/webapp:${VERSION:-latest}
echo "ℹ️  镜像推送需要先配置仓库认证"

# 示例5: 清理资源
echo "🧹 清理构建资源..."

# 删除构建的镜像
docker rmi webapp-example:${VERSION:-latest} || true
docker rmi webapp-multistage:${VERSION:-latest} || true

# 清理悬空镜像
docker image prune -f

# 清理临时文件
rm -rf /tmp/webapp /tmp/multistage /tmp/compose-example

echo "✅ Docker 构建示例完成！"

# 显示可用的 Docker 命令
echo "
📚 可用的 Docker 命令:
  - docker build    : 构建镜像
  - docker run      : 运行容器
  - docker push     : 推送镜像
  - docker pull     : 拉取镜像
  - docker images   : 列出镜像
  - docker ps       : 列出容器
  - docker-compose  : 编排多容器应用
  - docker system   : 系统管理命令

🔧 环境变量:
  - BUILD_NUMBER    : ${BUILD_NUMBER:-未设置}
  - VERSION         : ${VERSION:-未设置}
  - ENVIRONMENT     : ${ENVIRONMENT:-未设置}
  - DOCKER_HOST     : ${DOCKER_HOST:-未设置}
  - DOCKER_BUILDKIT : ${DOCKER_BUILDKIT:-未设置}
"
