#!/bin/bash

# =============================================================================
# Docker 构建测试脚本
# =============================================================================

set -e

echo "🔨 开始测试 Docker 构建..."

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_step() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# 检查前端构建
print_step "检查前端构建"
if [ ! -d "web/dist" ]; then
    print_error "前端dist目录不存在，请先运行 npm run build"
    exit 1
fi
print_success "前端构建文件存在"

# 检查必要文件
print_step "检查必要文件"
required_files=(
    "jdk-8u211-linux-x64.tar.gz"
    "apache-maven-3.8.8-bin.tar.gz"
    "ci-entrypoint-dind.sh"
    "docker-entrypoint.sh"
    "backend/requirements.txt"
    "web/nginx.conf"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "文件存在: $file"
    else
        print_error "文件缺失: $file"
        exit 1
    fi
done

# 开始构建
print_step "开始 Docker 构建"
print_info "构建镜像: liteops:test"

if docker build --platform linux/amd64 -t liteops:test .; then
    print_success "Docker 镜像构建成功！"
    
    # 显示镜像信息
    print_step "镜像信息"
    docker images liteops:test
    
    print_info "可以使用以下命令启动容器："
    echo "docker run -d --privileged --name liteops-test -p 80:80 -p 8900:8900 -p 2376:2376 liteops:test"
    
else
    print_error "Docker 镜像构建失败！"
    exit 1
fi
