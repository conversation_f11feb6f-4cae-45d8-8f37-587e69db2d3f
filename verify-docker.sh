#!/bin/bash

# =============================================================================
# Docker in Docker 功能验证脚本（精简版）
# =============================================================================

set -e

echo "🔍 验证 Docker in Docker 功能..."

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# 检查容器是否运行
if ! docker ps | grep -q liteops; then
    print_error "LiteOps 容器未运行，请先启动容器"
    exit 1
fi

print_info "检查 Docker 基本功能..."

# 测试 docker version
if docker exec liteops docker version >/dev/null 2>&1; then
    print_success "Docker 版本检查通过"
else
    print_error "Docker 版本检查失败"
    exit 1
fi

# 测试镜像拉取
print_info "测试镜像拉取功能..."
if docker exec liteops docker pull alpine:latest >/dev/null 2>&1; then
    print_success "镜像拉取功能正常"
else
    print_error "镜像拉取功能异常"
    exit 1
fi

# 测试容器运行
print_info "测试容器运行功能..."
if docker exec liteops docker run --rm alpine:latest echo "Hello Docker" >/dev/null 2>&1; then
    print_success "容器运行功能正常"
else
    print_error "容器运行功能异常"
    exit 1
fi

# 测试镜像构建
print_info "测试镜像构建功能..."
docker exec liteops bash -c 'echo "FROM alpine:latest" > /tmp/Dockerfile'
if docker exec liteops docker build -t test-build /tmp >/dev/null 2>&1; then
    print_success "镜像构建功能正常"
else
    print_error "镜像构建功能异常"
    exit 1
fi

# 清理测试资源
docker exec liteops docker rmi test-build alpine:latest >/dev/null 2>&1 || true
docker exec liteops rm -f /tmp/Dockerfile >/dev/null 2>&1 || true

print_success "🎉 Docker in Docker 功能验证完成！"
print_info "可以在 CI/CD 构建任务中使用 docker pull、push、build 等命令"
