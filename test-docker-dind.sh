#!/bin/bash

# =============================================================================
# Docker in Docker 功能测试脚本
# =============================================================================

set -e

echo "🧪 开始测试 Docker in Docker 功能..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_step() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# 检查容器是否运行
print_step "检查 LiteOps 容器状态"
if ! docker ps | grep -q liteops; then
    print_error "LiteOps 容器未运行，请先启动容器"
    exit 1
fi
print_success "LiteOps 容器正在运行"

# 测试 Docker 命令
print_step "测试基本 Docker 命令"

print_info "测试 docker version..."
if docker exec liteops docker version >/dev/null 2>&1; then
    print_success "docker version 命令正常"
else
    print_error "docker version 命令失败"
    exit 1
fi

print_info "测试 docker info..."
if docker exec liteops docker info >/dev/null 2>&1; then
    print_success "docker info 命令正常"
else
    print_error "docker info 命令失败"
    exit 1
fi

# 测试镜像操作
print_step "测试镜像操作"

print_info "拉取测试镜像..."
if docker exec liteops docker pull hello-world >/dev/null 2>&1; then
    print_success "镜像拉取成功"
else
    print_error "镜像拉取失败"
    exit 1
fi

print_info "列出镜像..."
if docker exec liteops docker images | grep -q hello-world; then
    print_success "镜像列表正常"
else
    print_error "镜像列表异常"
    exit 1
fi

# 测试容器操作
print_step "测试容器操作"

print_info "运行测试容器..."
if docker exec liteops docker run --rm hello-world >/dev/null 2>&1; then
    print_success "容器运行成功"
else
    print_error "容器运行失败"
    exit 1
fi

# 测试构建功能
print_step "测试构建功能"

print_info "创建测试 Dockerfile..."
docker exec liteops bash -c 'cat > /tmp/Dockerfile << EOF
FROM alpine:latest
RUN echo "Hello from Docker in Docker!" > /hello.txt
CMD cat /hello.txt
EOF'

print_info "构建测试镜像..."
if docker exec liteops docker build -t test-dind /tmp >/dev/null 2>&1; then
    print_success "镜像构建成功"
else
    print_error "镜像构建失败"
    exit 1
fi

print_info "运行构建的镜像..."
if docker exec liteops docker run --rm test-dind | grep -q "Hello from Docker in Docker!"; then
    print_success "构建的镜像运行正常"
else
    print_error "构建的镜像运行异常"
    exit 1
fi

# 清理测试资源
print_step "清理测试资源"
docker exec liteops docker rmi test-dind hello-world >/dev/null 2>&1 || true
docker exec liteops rm -f /tmp/Dockerfile >/dev/null 2>&1 || true
print_success "测试资源清理完成"

# 测试环境变量
print_step "检查环境变量"
DOCKER_HOST=$(docker exec liteops printenv DOCKER_HOST)
DOCKER_BUILDKIT=$(docker exec liteops printenv DOCKER_BUILDKIT)

if [ "$DOCKER_HOST" = "unix:///var/run/docker.sock" ]; then
    print_success "DOCKER_HOST 环境变量正确: $DOCKER_HOST"
else
    print_error "DOCKER_HOST 环境变量错误: $DOCKER_HOST"
fi

if [ "$DOCKER_BUILDKIT" = "1" ]; then
    print_success "DOCKER_BUILDKIT 环境变量正确: $DOCKER_BUILDKIT"
else
    print_error "DOCKER_BUILDKIT 环境变量错误: $DOCKER_BUILDKIT"
fi

print_step "测试完成"
print_success "🎉 所有 Docker in Docker 功能测试通过！"
print_info "您现在可以在 CI/CD 构建任务中使用标准的 Docker 命令了"
