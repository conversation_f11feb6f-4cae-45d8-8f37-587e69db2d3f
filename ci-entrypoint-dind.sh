#!/usr/bin/env bash
set -euo pipefail

# =============================================================================
# Docker in Docker (DinD) 启动脚本
# =============================================================================

echo "🐳 启动 Docker in Docker 环境..."

# 检查是否在特权模式下运行
if [ ! -w /sys/fs/cgroup ]; then
    echo "❌ 错误: 容器必须在特权模式下运行才能使用 Docker in Docker"
    echo "请使用 --privileged 参数启动容器"
    exit 1
fi

# 创建必要的目录
mkdir -p /var/lib/docker
mkdir -p /var/run/docker
mkdir -p /etc/docker

# 配置Docker daemon
cat > /etc/docker/daemon.json << 'EOF'
{
    "storage-driver": "overlay2",
    "hosts": ["unix:///var/run/docker.sock"],
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "10m",
        "max-file": "3"
    },
    "registry-mirrors": [
        "https://docker.mirrors.ustc.edu.cn",
        "https://hub-mirror.c.163.com"
    ],
    "insecure-registries": [],
    "live-restore": true,
    "userland-proxy": false,
    "experimental": false,
    "metrics-addr": "0.0.0.0:9323",
    "api-cors-header": "*"
}
EOF

# 启动Docker daemon
echo "🚀 启动 Docker daemon..."
dockerd \
    --host=unix:///var/run/docker.sock \
    --host=tcp://0.0.0.0:2376 \
    --storage-driver=overlay2 \
    --userland-proxy=false \
    --experimental=false \
    --live-restore=true &

# 等待Docker daemon启动
echo "⏳ 等待 Docker daemon 启动..."
timeout=60
while [ $timeout -gt 0 ]; do
    if docker version >/dev/null 2>&1; then
        echo "✅ Docker daemon 启动成功"
        break
    fi
    sleep 1
    timeout=$((timeout - 1))
done

if [ $timeout -eq 0 ]; then
    echo "❌ Docker daemon 启动超时"
    exit 1
fi

# 验证Docker功能
echo "🔍 验证 Docker 功能..."
docker version
docker info

# 设置环境变量
export DOCKER_HOST=unix:///var/run/docker.sock
export DOCKER_BUILDKIT=1

echo "🎉 Docker in Docker 环境启动完成"

# 执行传入的命令
exec "$@"
