# Docker in Docker (DinD) 配置说明

## 概述

LiteOps CI/CD 平台已从 rootless containerd + BuildKit 方案改造为 Docker in Docker (DinD) 方案，以提供更好的 Docker 兼容性和用户体验。

## 主要变化

### 1. 容器运行时变更
- **之前**: rootless containerd + nerdctl + BuildKit
- **现在**: Docker Engine + Docker CLI

### 2. 启动脚本变更
- **之前**: `ci-entrypoint-rootful.sh` (启动 containerd + buildkitd)
- **现在**: `ci-entrypoint-dind.sh` (启动 Docker daemon)

### 3. 环境变量变更
- **之前**: `BUILDKIT_HOST=unix:///run/buildkit/buildkitd.sock`
- **现在**: `DOCKER_HOST=unix:///var/run/docker.sock`

## 部署要求

### 特权模式
Docker in Docker 需要容器在特权模式下运行：

```bash
docker run --privileged ...
```

### 端口映射
新增 Docker API 端口映射：

```bash
-p 2376:2376  # Docker API 端口
```

### 卷挂载
为了提高性能，建议挂载 Docker 数据目录：

```bash
-v /var/lib/docker
```

## 使用方法

### 1. 自动部署
使用提供的启动脚本：

```bash
./start-containers.sh
```

脚本会自动：
- 构建支持 Docker in Docker 的镜像
- 以特权模式启动容器
- 配置必要的端口映射和卷挂载

### 2. 手动部署
如果需要手动部署，请确保包含以下参数：

```bash
docker run -d \
    --name liteops \
    --privileged \
    -v /var/lib/docker \
    -p 80:80 \
    -p 8900:8900 \
    -p 2376:2376 \
    liteops:latest
```

## 构建任务中的 Docker 使用

在 CI/CD 构建任务中，现在可以直接使用标准的 Docker 命令：

```bash
# 构建镜像
docker build -t myapp:latest .

# 推送镜像
docker push myapp:latest

# 运行容器
docker run -d myapp:latest

# 使用 Docker Compose
docker-compose up -d
```

## 配置说明

### Docker Daemon 配置
系统会自动创建 `/etc/docker/daemon.json` 配置文件，包含：

- **存储驱动**: overlay2
- **镜像加速**: 使用国内镜像源
- **日志配置**: 限制日志大小
- **API 访问**: 支持 Unix socket 和 TCP 访问

### 环境变量
构建任务中会自动设置以下环境变量：

- `DOCKER_HOST=unix:///var/run/docker.sock`
- `DOCKER_BUILDKIT=1`

## 故障排除

### 1. 权限问题
如果遇到权限错误，请确保：
- 容器以 `--privileged` 模式运行
- 检查 `/sys/fs/cgroup` 是否可写

### 2. Docker daemon 启动失败
检查容器日志：
```bash
docker logs liteops
```

常见问题：
- 缺少特权模式
- 端口冲突
- 存储空间不足

### 3. 构建任务中 Docker 命令失败
确认：
- Docker daemon 已正常启动
- `DOCKER_HOST` 环境变量正确设置
- Docker socket 文件存在且可访问

## 性能优化

### 1. 使用卷挂载
为了避免每次重启容器时重新下载镜像：

```bash
-v docker-data:/var/lib/docker
```

### 2. 镜像缓存
配置了国内镜像源以加速镜像拉取：
- https://docker.mirrors.ustc.edu.cn
- https://hub-mirror.c.163.com

### 3. 构建缓存
启用了 Docker BuildKit 以提高构建性能：
- 并行构建
- 增量构建
- 缓存挂载

## 安全注意事项

1. **特权模式**: Docker in Docker 需要特权模式，请确保在受信任的环境中运行
2. **网络隔离**: 建议使用专用网络隔离 CI/CD 容器
3. **资源限制**: 考虑设置内存和 CPU 限制以防止资源耗尽
4. **镜像安全**: 定期更新基础镜像以获取安全补丁

## 迁移指南

从旧版本迁移时：

1. 停止现有容器
2. 重新构建镜像（使用新的 Dockerfile）
3. 使用新的启动脚本部署
4. 验证 Docker 功能正常

构建脚本无需修改，Docker 命令保持兼容。
